import React, { useState, useEffect } from 'react';
import { 
  X, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Building, 
  Award, 
  Clock,
  AlertCircle,
  CheckCircle,
  Users,
  Briefcase,
  Heart,
  Globe,
  Home,
  UserCheck
} from 'lucide-react';
import { HREmployeeProfile, HREmployeeBadge, HRLeaveRecord } from '../types/hr';
import { 
  fetchEmployeeBadgesHR, 
  fetchEmployeeLeaveHistoryHR, 
  fetchEmployeeSkillsHR 
} from '../data/supabaseHR';
import BadgeImage from './BadgeImage';
import ProfilePicture from './ProfilePicture';

interface HREmployeeDetailModalProps {
  employee: HREmployeeProfile;
  onClose: () => void;
}

const HREmployeeDetailModal: React.FC<HREmployeeDetailModalProps> = ({ employee, onClose }) => {
  const [badges, setBadges] = useState<HREmployeeBadge[]>([]);
  const [leaveHistory, setLeaveHistory] = useState<HRLeaveRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'badges' | 'leave' | 'contact'>('overview');

  useEffect(() => {
    loadEmployeeDetails();
  }, [employee.id]);

  const loadEmployeeDetails = async () => {
    try {
      setLoading(true);
      const [badgeData, leaveData] = await Promise.all([
        fetchEmployeeBadgesHR(employee.id),
        fetchEmployeeLeaveHistoryHR(employee.id)
      ]);
      
      setBadges(badgeData);
      setLeaveHistory(leaveData);
    } catch (error) {
      console.error('Error loading employee details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const calculateDaysRemaining = (expiryDate?: string): number => {
    if (!expiryDate) return -1;
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getLeaveStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'badges', label: 'Badges', icon: Award },
    { id: 'leave', label: 'Leave History', icon: Calendar },
    { id: 'contact', label: 'Contact Info', icon: Phone }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <ProfilePicture
                userId={employee.user_id || employee.id}
                name={employee.full_name}
                email={employee.email}
                profilePictureUrl={employee.profile_picture_url}
                size="xl"
                className="ring-4 ring-white ring-opacity-30"
              />
              <div>
                <h2 className="text-2xl font-bold">{employee.full_name}</h2>
                <p className="text-blue-100">{employee.designation}</p>
                <p className="text-blue-200 text-sm">ID: {employee.employee_id}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 p-2 rounded-full hover:bg-white hover:bg-opacity-20"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading employee details...</span>
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Employment Information */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Briefcase size={20} className="text-blue-600" />
                      Employment Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Department</label>
                        <p className="text-gray-900">{employee.department || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Team</label>
                        <p className="text-gray-900">{employee.team_name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Employment Type</label>
                        <p className="text-gray-900">{employee.employment_type || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Joining Date</label>
                        <p className="text-gray-900">{formatDate(employee.joining_date)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Years of Service</label>
                        <p className="text-gray-900">{employee.years_of_service} years</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Manager</label>
                        <p className="text-gray-900">{employee.manager_name || 'N/A'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <User size={20} className="text-green-600" />
                      Personal Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                        <p className="text-gray-900">{formatDate(employee.date_of_birth)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Gender</label>
                        <p className="text-gray-900">{employee.gender || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Marital Status</label>
                        <p className="text-gray-900">{employee.marital_status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Nationality</label>
                        <p className="text-gray-900">{employee.nationality || 'N/A'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Leave Balance Summary */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Calendar size={20} className="text-orange-600" />
                      Leave Balance
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{employee.casual_leave_balance}</p>
                        <p className="text-sm text-gray-500">Casual Leave</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{employee.sick_leave_balance}</p>
                        <p className="text-sm text-gray-500">Sick Leave</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">{employee.annual_leave_balance}</p>
                        <p className="text-sm text-gray-500">Annual Leave</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{employee.total_leave_balance}</p>
                        <p className="text-sm text-gray-500">Total Balance</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Badges Tab */}
              {activeTab === 'badges' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">Professional Badges</h3>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {badges.filter(b => b.status === 'active').length} Active
                    </span>
                  </div>
                  
                  {badges.length === 0 ? (
                    <div className="text-center py-12">
                      <Award className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No badges assigned</h3>
                      <p className="mt-1 text-sm text-gray-500">This employee hasn't been assigned any badges yet.</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {badges.map((badge) => {
                        const daysRemaining = calculateDaysRemaining(badge.expiry_date);
                        return (
                          <div key={badge.badge_id} className="border rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <div 
                                className="w-12 h-12 rounded-lg flex items-center justify-center"
                                style={{ backgroundColor: badge.badge_color + '20' }}
                              >
                                <Award size={24} style={{ color: badge.badge_color }} />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{badge.badge_name}</h4>
                                <p className="text-sm text-gray-600">{badge.badge_description}</p>
                                <div className="mt-2 space-y-1">
                                  <div className="flex items-center gap-2">
                                    <Calendar size={14} className="text-gray-400" />
                                    <span className="text-xs text-gray-500">
                                      Assigned: {formatDate(badge.assigned_date)}
                                    </span>
                                  </div>
                                  {badge.expiry_date && (
                                    <div className="flex items-center gap-2">
                                      <Clock size={14} className="text-gray-400" />
                                      <span className={`text-xs ${daysRemaining < 30 ? 'text-yellow-600' : 'text-gray-500'}`}>
                                        {daysRemaining > 0 ? `${daysRemaining} days remaining` : 'Expired'}
                                        {daysRemaining < 30 && daysRemaining > 0 && ' ⚠️'}
                                      </span>
                                    </div>
                                  )}
                                  {badge.assigned_by_name && (
                                    <div className="flex items-center gap-2">
                                      <UserCheck size={14} className="text-gray-400" />
                                      <span className="text-xs text-gray-500">
                                        By: {badge.assigned_by_name}
                                      </span>
                                    </div>
                                  )}
                                </div>
                                <span className={`inline-block mt-2 px-2 py-1 text-xs font-medium rounded-full ${
                                  badge.status === 'active' ? 'bg-green-100 text-green-800' :
                                  badge.status === 'expired' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {badge.status}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}

              {/* Leave History Tab */}
              {activeTab === 'leave' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">Leave History</h3>
                    <span className="text-sm text-gray-500">
                      {leaveHistory.length} total records
                    </span>
                  </div>
                  
                  {leaveHistory.length === 0 ? (
                    <div className="text-center py-12">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No leave history</h3>
                      <p className="mt-1 text-sm text-gray-500">This employee hasn't taken any leave yet.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {leaveHistory.slice(0, 10).map((leave) => (
                        <div key={leave.leave_id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium text-gray-900">{leave.leave_type}</h4>
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getLeaveStatusColor(leave.status)}`}>
                                  {leave.status}
                                </span>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-500">Duration:</span>
                                  <p className="text-gray-900">
                                    {formatDate(leave.start_date)} - {formatDate(leave.end_date)}
                                  </p>
                                  <p className="text-gray-600">{leave.days_count} days</p>
                                </div>
                                <div>
                                  <span className="text-gray-500">Applied:</span>
                                  <p className="text-gray-900">{formatDate(leave.applied_date)}</p>
                                </div>
                                {leave.approved_by_name && (
                                  <div>
                                    <span className="text-gray-500">Approved by:</span>
                                    <p className="text-gray-900">{leave.approved_by_name}</p>
                                  </div>
                                )}
                              </div>
                              {leave.reason && (
                                <div className="mt-2">
                                  <span className="text-gray-500 text-sm">Reason:</span>
                                  <p className="text-gray-900 text-sm">{leave.reason}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Contact Information Tab */}
              {activeTab === 'contact' && (
                <div className="space-y-6">
                  {/* Contact Details */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Phone size={20} className="text-blue-600" />
                      Contact Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Work Email</label>
                        <p className="text-gray-900 flex items-center gap-2">
                          <Mail size={16} className="text-gray-400" />
                          {employee.email}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Personal Email</label>
                        <p className="text-gray-900 flex items-center gap-2">
                          <Mail size={16} className="text-gray-400" />
                          {employee.personal_email || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Phone</label>
                        <p className="text-gray-900 flex items-center gap-2">
                          <Phone size={16} className="text-gray-400" />
                          {employee.phone || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Location</label>
                        <p className="text-gray-900 flex items-center gap-2">
                          <MapPin size={16} className="text-gray-400" />
                          {employee.location || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Address Information */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Home size={20} className="text-green-600" />
                      Address Information
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Current Address</label>
                        <p className="text-gray-900">{employee.current_address || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Permanent Address</label>
                        <p className="text-gray-900">{employee.permanent_address || 'N/A'}</p>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-500">City</label>
                          <p className="text-gray-900">{employee.city || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">State</label>
                          <p className="text-gray-900">{employee.state || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Postal Code</label>
                          <p className="text-gray-900">{employee.postal_code || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Emergency Contact */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <AlertCircle size={20} className="text-red-600" />
                      Emergency Contact
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Name</label>
                        <p className="text-gray-900">{employee.emergency_contact_name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Phone</label>
                        <p className="text-gray-900">{employee.emergency_contact_phone || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Relationship</label>
                        <p className="text-gray-900">{employee.emergency_contact_relationship || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HREmployeeDetailModal;
